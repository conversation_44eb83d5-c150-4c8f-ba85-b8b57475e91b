# Sistema de Control de Inventario

Este es un sistema de control de inventario desarrollado con React, TypeScript y Vite. El sistema permite gestionar tarjetas de almacén con diferentes métodos de valuación de inventario.

## Características

- 📦 Gestión de tarjetas de almacén
- 🔄 Métodos de valuación de inventario:
  - PEPS (Primeras Entradas Primeras Salidas)
  - UEPS (Últimas Entradas Primeras Salidas)
  - Promedio Ponderado
- 📝 Registro de movimientos:
  - Compras
  - Ventas
  - Devoluciones
- 💾 Persistencia de datos en localStorage
- 🎨 Interfaz moderna y responsive
- 🌓 Soporte para modo oscuro y claro

## Tecnologías Utilizadas

- Next.js (React)
- TypeScript
- Bootstrap CSS
- shadcn/ui
- Framer Motion
- Vite
- Zustand (Gestión de estado)
- Tailwind CSS

## Instalación

1. Clona el repositorio:
```bash
git clone [URL_DEL_REPOSITORIO]
```

2. Instala las dependencias:
```bash
cd almacen-inventario
npm install
```

3. Inicia el servidor de desarrollo:
```bash
npm run dev
```

## Uso

1. Crea una nueva tarjeta de almacén haciendo clic en el botón "Nueva Tarjeta"
2. Completa la información del producto:
   - Nombre del artículo
   - Referencia
   - Proveedor
   - Ubicación
   - Límites de inventario
3. Selecciona el método de valuación deseado
4. Registra los movimientos de inventario:
   - Compras
   - Ventas
   - Devoluciones
5. Visualiza el historial de movimientos y los cálculos automáticos

## Estructura del Proyecto

```
almacen-inventario/
├── src/
│   ├── components/
│   │   ├── InventoryCard.tsx
│   │   ├── AddMovementModal.tsx
│   │   └── AddCardModal.tsx
│   ├── store/
│   │   └── inventoryStore.ts
│   ├── types/
│   │   └── inventory.ts
│   ├── utils/
│   │   └── inventoryCalculations.ts
│   ├── App.tsx
│   └── main.tsx
├── public/
├── index.html
└── package.json
```

## Contribución

1. Haz un fork del proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más Detalles.
