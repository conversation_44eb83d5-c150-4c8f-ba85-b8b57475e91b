{"name": "almacen-inventario", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@types/react-bootstrap": "^1.0.1", "@types/react-datepicker": "^6.2.0", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.13.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.309.0", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}