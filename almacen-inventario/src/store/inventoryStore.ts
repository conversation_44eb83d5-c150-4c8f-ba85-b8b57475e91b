import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { InventoryState, InventoryCard, InventoryMovement, ValuationMethod } from '../types/inventory';

export const useInventoryStore = create<InventoryState>()(
  persist(
    (set) => ({
      cards: [],
      addCard: (card: InventoryCard) =>
        set((state: InventoryState) => ({
          cards: [...state.cards, card],
        })),
      updateCard: (numero_tarjeta: string, updatedCard: InventoryCard) =>
        set((state: InventoryState) => ({
          cards: state.cards.map((card) =>
            card.product.numero_tarjeta === numero_tarjeta ? updatedCard : card
          ),
        })),
      deleteCard: (numero_tarjeta: string) =>
        set((state: InventoryState) => ({
          cards: state.cards.filter((card) => card.product.numero_tarjeta !== numero_tarjeta),
        })),
      addMovement: (numero_tarjeta: string, movement: InventoryMovement) =>
        set((state: InventoryState) => ({
          cards: state.cards.map((card) => {
            if (card.product.numero_tarjeta === numero_tarjeta) {
              const updatedMovements = [...card.movements, movement];
              return {
                ...card,
                movements: updatedMovements,
              };
            }
            return card;
          }),
        })),
      setValuationMethod: (numero_tarjeta: string, method: ValuationMethod) =>
        set((state: InventoryState) => ({
          cards: state.cards.map((card) =>
            card.product.numero_tarjeta === numero_tarjeta
              ? { ...card, valuationMethod: method }
              : card
          ),
        })),
      clearData: () => set({ cards: [] }),
      clearCardData: (cardNumber: string) => set((state) => ({
        cards: state.cards.map(card =>
          card.product.numero_tarjeta === cardNumber
            ? { ...card, movements: [] }
            : card
        )
      })),
    }),
    {
      name: 'inventory-storage',
    }
  )
); 