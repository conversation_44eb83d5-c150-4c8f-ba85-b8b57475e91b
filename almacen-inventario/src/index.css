@import 'bootstrap/dist/css/bootstrap.min.css';
@import 'bootstrap-icons/font/bootstrap-icons.css';

:root {
  --primary-color: #4f46e5;
  --primary-dark: #4338ca;
  --primary-light: #818cf8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  --background-color: #f8fafc;
  --text-color: #1e293b;
  --card-bg: #ffffff;
  --card-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
  --gradient-danger: linear-gradient(135deg, var(--danger-color), #dc2626);
  --gradient-info: linear-gradient(135deg, var(--info-color), #2563eb);
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
}

h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-color);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.card {
  border: none;
  border-radius: 1rem;
  box-shadow: var(--card-shadow);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  background-color: var(--card-bg);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.card-header {
  border-radius: 1rem 1rem 0 0 !important;
  background: var(--gradient-primary) !important;
  padding: 1.25rem;
}

.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  background-color: #f8fafc;
  font-weight: 600;
  white-space: nowrap;
  padding: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.table td {
  vertical-align: middle;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.table tbody tr:hover {
  background-color: #f8fafc;
}

.btn {
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.btn-primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(79, 70, 229, 0.3);
}

.btn-danger {
  background: var(--gradient-danger);
  border: none;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(239, 68, 68, 0.3);
}

.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.modal-header {
  border-radius: 1rem 1rem 0 0;
  background: var(--gradient-primary);
  color: white;
  padding: 1.25rem;
}

.form-control {
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.alert {
  border: none;
  border-radius: 1rem;
  padding: 1rem 1.25rem;
}

.alert-info {
  background: var(--gradient-info);
  color: white;
}

.alert-warning {
  background: var(--gradient-warning);
  color: white;
}

.text-success {
  color: var(--success-color) !important;
}

.text-danger {
  color: var(--danger-color) !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background: var(--gradient-primary) !important;
}

.bg-success {
  background: var(--gradient-success) !important;
}

.bg-danger {
  background: var(--gradient-danger) !important;
}

.bg-info {
  background: var(--gradient-info) !important;
}

/* Animaciones */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-in;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-in;
}

/* Responsive */
@media (max-width: 768px) {
  h1 {
    font-size: 1.75rem;
  }

  .card-header {
    padding: 1rem;
  }

  .table-responsive {
    margin: 0 -1rem;
    padding: 0 1rem;
    width: calc(100% + 2rem);
  }

  .table th, .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .form-control {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .input-group-lg > .form-control,
  .input-group-lg > .input-group-text {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .alert {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 576px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .card {
    margin-bottom: 1rem;
  }

  .table th, .table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
  }

  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .modal-dialog {
    margin: 0.25rem;
  }

  .form-label {
    font-size: 0.875rem;
  }

  .input-group {
    flex-direction: column;
  }

  .input-group > .form-control,
  .input-group > .input-group-text {
    border-radius: 0.5rem !important;
    margin-bottom: 0.5rem;
  }

  .input-group > .input-group-text {
    width: 100%;
    justify-content: center;
  }
}

/* Mejoras para la tabla en dispositivos móviles */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table th, .table td {
    white-space: nowrap;
  }

  .table td:first-child,
  .table th:first-child {
    position: sticky;
    left: 0;
    background-color: var(--card-bg);
    z-index: 1;
  }
}

/* Mejoras para los modales en dispositivos móviles */
@media (max-width: 576px) {
  .modal-content {
    border-radius: 0.5rem;
  }

  .modal-header {
    padding: 0.75rem;
  }

  .modal-body {
    padding: 0.75rem;
  }

  .modal-footer {
    padding: 0.75rem;
  }

  .modal-footer .btn {
    margin: 0.25rem 0;
  }
}

/* Mejoras para el DatePicker en dispositivos móviles */
@media (max-width: 576px) {
  .react-datepicker-wrapper {
    width: 100%;
  }

  .react-datepicker__input-container input {
    width: 100%;
  }
}
