import type { InventoryMovement, ValuationMethod } from '../types/inventory';

interface CalculatedCost {
  costoTotal: number;
  costoUnitario: number;
}

export const calculatePEPS = (movements: InventoryMovement[], cantidad: number): CalculatedCost => {
  let unidadesRestantes = cantidad;
  let costoTotal = 0;
  
  // Ordenar movimientos por fecha ascendente
  const movimientosOrdenados = [...movements].sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime());

  // Reconstruir el inventario real por lotes (entradas y salidas)
  const inventario: { unidades: number; costo: number }[] = [];
  
  for (const movimiento of movimientosOrdenados) {
    if (movimiento.entrada > 0) {
      // Nueva entrada: agregar lote
    inventario.push({
      unidades: movimiento.entrada,
      costo: movimiento.costo_unitario
    });
  }
    if (movimiento.salida > 0) {
      // Salida: descontar de los lotes más antiguos
      let unidadesPorSalir = movimiento.salida;
      while (unidadesPorSalir > 0 && inventario.length > 0) {
    const lote = inventario[0];
        const unidadesAUsar = Math.min(unidadesPorSalir, lote.unidades);
        lote.unidades -= unidadesAUsar;
        unidadesPorSalir -= unidadesAUsar;
        if (lote.unidades === 0) {
          inventario.shift();
        }
      }
    }
  }

  // Ahora, para la nueva salida, descontar de los lotes disponibles
  const inventarioCopia = inventario.map(lote => ({ ...lote }));
  while (unidadesRestantes > 0 && inventarioCopia.length > 0) {
    const lote = inventarioCopia[0];
    const unidadesAUsar = Math.min(unidadesRestantes, lote.unidades);
    costoTotal += unidadesAUsar * lote.costo;
    unidadesRestantes -= unidadesAUsar;
    lote.unidades -= unidadesAUsar;
    if (lote.unidades === 0) {
      inventarioCopia.shift();
    }
  }

  return {
    costoTotal,
    costoUnitario: cantidad > 0 ? costoTotal / cantidad : 0
  };
};

export const calculateUEPS = (movements: InventoryMovement[], cantidad: number): CalculatedCost => {
  let unidadesRestantes = cantidad;
  let costoTotal = 0;
  
  // Ordenamos los movimientos por fecha ascendente
  const movimientosOrdenados = [...movements]
    .filter(m => m.entrada > 0) // Solo consideramos entradas
    .sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime());

  // Creamos una pila de inventario
  const inventario: { unidades: number; costo: number }[] = [];
  
  // Llenamos el inventario con las entradas
  for (const movimiento of movimientosOrdenados) {
    inventario.push({
      unidades: movimiento.entrada,
      costo: movimiento.costo_unitario
    });
  }

  // Consumimos las unidades más recientes primero
  while (unidadesRestantes > 0 && inventario.length > 0) {
    const lote = inventario[inventario.length - 1];
    const unidadesAUsar = Math.min(unidadesRestantes, lote.unidades);
    
    costoTotal += unidadesAUsar * lote.costo;
    unidadesRestantes -= unidadesAUsar;
    lote.unidades -= unidadesAUsar;

    if (lote.unidades === 0) {
      inventario.pop();
    }
  }
  console.log("Inventario final UEPS:", inventario);

  return {
    costoTotal,
    costoUnitario: cantidad > 0 ? costoTotal / cantidad : 0
  };
};

export const calculateSalidasProm = (movements: InventoryMovement[], cantidad: number): CalculatedCost => {
  let unidadesRestantes = cantidad;
  let costoTotal = 0;
  
  // Ordenamos los movimientos por fecha ascendente
  const movimientosOrdenados = [...movements]
    .filter(m => m.entrada > 0) // Solo consideramos entradas
    .sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime());

  // Creamos una pila de inventario
  const inventario: { unidades: number; costo: number }[] = [];
  
  // Llenamos el inventario con las entradas
  for (const movimiento of movimientosOrdenados) {
    inventario.push({
      unidades: movimiento.entrada,
      costo: movimiento.costo_unitario
    });
  }

  // Consumimos las unidades más recientes primero
  while (unidadesRestantes > 0 && inventario.length > 0) {
    const lote = inventario[inventario.length - 1];
    const unidadesAUsar = Math.min(unidadesRestantes, lote.unidades);
    
    costoTotal += unidadesAUsar * lote.costo;
    unidadesRestantes -= unidadesAUsar;
    lote.unidades -= unidadesAUsar;

    if (lote.unidades === 0) {
      inventario.pop();
    }
  }

  return {
    costoTotal,
    costoUnitario: cantidad > 0 ? costoTotal / cantidad : 0
  };
};

export const calculateAverageCost = (movements: InventoryMovement[]): number => {
  let unidadesTotales = 0;
  let valorTotal = 0;
  let lastCostoMedio = 0;

  for (const movimiento of movements) {
    // Si es entrada o devolución sobre venta, recalcula el promedio
    if (movimiento.entrada > 0) {
      valorTotal += movimiento.entrada * movimiento.costo_unitario;
      unidadesTotales += movimiento.entrada;
      lastCostoMedio = unidadesTotales > 0 ? valorTotal / unidadesTotales : 0;
    } else if (movimiento.devolucion_sobre_ventas && movimiento.devolucion_sobre_ventas > 0) {
      valorTotal += movimiento.devolucion_sobre_ventas * movimiento.costo_unitario;
      unidadesTotales += movimiento.devolucion_sobre_ventas;
      lastCostoMedio = unidadesTotales > 0 ? valorTotal / unidadesTotales : 0;
    } else if (movimiento.salida > 0) {
      // Salida: solo restar unidades y valor, pero el costo medio no cambia
      valorTotal -= movimiento.salida * lastCostoMedio;
      unidadesTotales -= movimiento.salida;
      // lastCostoMedio se mantiene igual
    } else if (movimiento.devolucion_sobre_compras && movimiento.devolucion_sobre_compras > 0) {
      // Devolución sobre compra: solo restar unidades y valor, pero el costo medio no cambia
      valorTotal -= movimiento.devolucion_sobre_compras * lastCostoMedio;
      unidadesTotales -= movimiento.devolucion_sobre_compras;
      // lastCostoMedio se mantiene igual
    }
  }

  return lastCostoMedio;
};

export const calculateInventoryValue = (
  movements: InventoryMovement[],
  method: ValuationMethod
): number => {
  const ultimoMovimiento = movements[movements.length - 1];
  if (!ultimoMovimiento) return 0;

  const unidadesFinales = ultimoMovimiento.inv_final;

  switch (method) {
    case 'PEPS':
      return calculatePEPS(movements, unidadesFinales).costoTotal;
    case 'UEPS':
      return calculateUEPS(movements, unidadesFinales).costoTotal;
    case 'PROMEDIO':
      return calculateAverageCost(movements) * unidadesFinales;
    default:
      return 0;
  }
};

export const calculateNewMovement = (
  movements: InventoryMovement[],
  newMovement: InventoryMovement,
  valuationMethod: ValuationMethod
): InventoryMovement => {
  const ultimoMovimiento = movements[movements.length - 1];
  const inv_inicial = ultimoMovimiento ? ultimoMovimiento.inv_final : 0;
  const inv_final =
    inv_inicial +
    (newMovement.entrada || 0) -
    (newMovement.salida || 0) +
    (newMovement.devolucion_sobre_ventas || 0) -
    (newMovement.devolucion_sobre_compras || 0);

  let costo_unitario = newMovement.costo_unitario;
  let costo_medio = 0;
  let debe = 0;
  let haber = 0;
  let saldo = 0;

  // Entradas normales
  if (newMovement.entrada > 0) {
    debe += newMovement.entrada * costo_unitario;
    if (valuationMethod === 'PROMEDIO') {
      costo_medio = calculateAverageCost([...movements, newMovement]);
    }
  }

  // Salidas normales
  if (newMovement.salida > 0) {
    let costoSalida = 0;
    let costoUnitarioSalida = 0;
    if (valuationMethod === 'PEPS') {
      const { costoTotal } = calculatePEPS(movements, newMovement.salida);
      costoSalida = costoTotal;
      costoUnitarioSalida = newMovement.salida > 0 ? costoTotal / newMovement.salida : 0;
    } else if (valuationMethod === 'UEPS') {
      const { costoTotal } = calculateUEPS(movements, newMovement.salida);
      costoSalida = costoTotal;
      costoUnitarioSalida = newMovement.salida > 0 ? costoTotal / newMovement.salida : 0;
    } else if (valuationMethod === 'PROMEDIO') {
      costo_medio = calculateAverageCost(movements);
      costoUnitarioSalida = costo_medio;
      costoSalida = costoUnitarioSalida * newMovement.salida;
    }
    haber += costoSalida;
    costo_unitario = costoUnitarioSalida;
  }

  // Devolución sobre ventas (entrada)
  if (newMovement.devolucion_sobre_ventas && newMovement.devolucion_sobre_ventas > 0) {
    let costoDevVenta = 0;
    let costoUnitarioDevVenta = 0;
    if (valuationMethod === 'PEPS') {
      const { costoTotal, costoUnitario } = calculatePEPS(movements, newMovement.devolucion_sobre_ventas);
      costoDevVenta = costoTotal;
      costoUnitarioDevVenta = costoUnitario;
    } else if (valuationMethod === 'UEPS') {
      const { costoTotal, costoUnitario } = calculateUEPS(movements, newMovement.devolucion_sobre_ventas);
      costoDevVenta = costoTotal;
      costoUnitarioDevVenta = costoUnitario;
    } else if (valuationMethod === 'PROMEDIO') {
      const { costoTotal, costoUnitario } = calculateSalidasProm(movements, newMovement.devolucion_sobre_ventas);
      costoDevVenta = costoTotal;
      costoUnitarioDevVenta = costoUnitario;
    }
    debe += costoDevVenta;
    if (valuationMethod === 'PROMEDIO') {
      costo_unitario = costoUnitarioDevVenta;
      costo_medio = calculateAverageCost([...movements, {
        ...newMovement,
        costo_unitario: costoUnitarioDevVenta,
        entrada: 0,
        salida: 0,
        devolucion_sobre_ventas: newMovement.devolucion_sobre_ventas,
        devolucion_sobre_compras: 0
      }]);
    }
  }

  // Devolución sobre compras (salida)
  if (newMovement.devolucion_sobre_compras && newMovement.devolucion_sobre_compras > 0) {
    let costoDevCompra = 0;
    let costoUnitarioDevCompra = 0;
    if (valuationMethod === 'PEPS') {
      const { costoTotal, costoUnitario } = calculatePEPS(movements, newMovement.devolucion_sobre_compras);
      costoDevCompra = costoTotal;
      costoUnitarioDevCompra = costoUnitario;
    } else if (valuationMethod === 'UEPS') {
      const { costoTotal, costoUnitario } = calculateUEPS(movements, newMovement.devolucion_sobre_compras);
      costoDevCompra = costoTotal;
      costoUnitarioDevCompra = costoUnitario;
    } else if (valuationMethod === 'PROMEDIO') {
      const { costoTotal, costoUnitario } = calculateSalidasProm(movements, newMovement.devolucion_sobre_compras);
      costoDevCompra = costoTotal;
      costoUnitarioDevCompra = costoUnitario;
    }
    haber += costoDevCompra;
    if (valuationMethod === 'PROMEDIO') {
      costo_unitario = costoUnitarioDevCompra;
      costo_medio = calculateAverageCost([...movements, {
        ...newMovement,
        costo_unitario: costoUnitarioDevCompra,
        entrada: 0,
        salida: 0,
        devolucion_sobre_ventas: 0,
        devolucion_sobre_compras: newMovement.devolucion_sobre_compras
      }]);
    }
  }

  saldo = (ultimoMovimiento?.saldo || 0) + debe - haber;

  return {
    ...newMovement,
    inv_inicial,
    inv_final,
    costo_unitario,
    costo_medio,
    debe,
    haber,
    saldo
  };
};

// Devuelve el desglose de la salida por lotes consumidos (PEPS)
export const desglosePEPS = (movements: InventoryMovement[], cantidad: number) => {
  let unidadesRestantes = cantidad;
  // Ordenar movimientos por fecha ascendente
  const movimientosOrdenados = [...movements].sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime());

  // Reconstruir el inventario real por lotes (entradas y salidas)
  const inventario: { unidades: number; costo: number }[] = [];
  for (const movimiento of movimientosOrdenados) {
    if (movimiento.entrada > 0) {
    inventario.push({
      unidades: movimiento.entrada,
      costo: movimiento.costo_unitario
    });
  }
    if (movimiento.salida > 0) {
      let unidadesPorSalir = movimiento.salida;
      while (unidadesPorSalir > 0 && inventario.length > 0) {
        const lote = inventario[0];
        const unidadesAUsar = Math.min(unidadesPorSalir, lote.unidades);
        lote.unidades -= unidadesAUsar;
        unidadesPorSalir -= unidadesAUsar;
        if (lote.unidades === 0) {
          inventario.shift();
        }
      }
    }
  }

  // Ahora, para la nueva salida, desglose por lotes disponibles
  const desglose: { cantidad: number; costoUnitario: number }[] = [];
  const inventarioCopia = inventario.map(lote => ({ ...lote }));
  while (unidadesRestantes > 0 && inventarioCopia.length > 0) {
    const lote = inventarioCopia[0];
    const unidadesAUsar = Math.min(unidadesRestantes, lote.unidades);
    if (unidadesAUsar > 0) {
      desglose.push({ cantidad: unidadesAUsar, costoUnitario: lote.costo });
      unidadesRestantes -= unidadesAUsar;
      lote.unidades -= unidadesAUsar;
    }
    if (lote.unidades === 0) inventarioCopia.shift();
  }
  if (unidadesRestantes > 0) throw new Error('No hay suficiente inventario');
  return desglose;
};

// Devuelve el desglose de la salida por lotes consumidos (UEPS)
export const desgloseUEPS = (movements: InventoryMovement[], cantidad: number) => {
  let unidadesRestantes = cantidad;
  const movimientosOrdenados = [...movements]
    .filter(m => m.entrada > 0)
    .sort((a, b) => new Date(a.fecha).getTime() - new Date(b.fecha).getTime());

  const inventario: { unidades: number; costo: number }[] = [];
  for (const movimiento of movimientosOrdenados) {
    inventario.push({
      unidades: movimiento.entrada,
      costo: movimiento.costo_unitario
    });
  }

  const desglose: { cantidad: number; costoUnitario: number }[] = [];
  while (unidadesRestantes > 0 && inventario.length > 0) {
    const lote = inventario[inventario.length - 1];
    const unidadesAUsar = Math.min(unidadesRestantes, lote.unidades);
    if (unidadesAUsar > 0) {
      desglose.push({ cantidad: unidadesAUsar, costoUnitario: lote.costo });
      unidadesRestantes -= unidadesAUsar;
      lote.unidades -= unidadesAUsar;
    }
    if (lote.unidades === 0) inventario.pop();
  }
  if (unidadesRestantes > 0) throw new Error('No hay suficiente inventario');
  return desglose;
};

// Función para desglosar devoluciones sobre ventas (similar a desglosePEPS/desgloseUEPS)
export const desgloseDevolucionVentas = (movements: InventoryMovement[], cantidad: number, method: ValuationMethod) => {
  if (method === 'PEPS') {
    return desglosePEPS(movements, cantidad);
  } else if (method === 'UEPS') {
    return desgloseUEPS(movements, cantidad);
  } else {
    // Para PROMEDIO, solo devolvemos un lote con el costo promedio
    const costoPromedio = calculateAverageCost(movements);
    return [{ cantidad, costoUnitario: costoPromedio }];
  }
};

// Calcula movimientos desglosados para salidas PEPS/UEPS
export const calculateNewMovementsWithDesglose = (
  movements: InventoryMovement[],
  newMovement: InventoryMovement,
  valuationMethod: ValuationMethod
): InventoryMovement[] => {
  // Si no es salida ni devolución sobre ventas, o es promedio, usar la función normal
  if (
    ((!newMovement.salida || newMovement.salida <= 0) && 
     (!newMovement.devolucion_sobre_ventas || newMovement.devolucion_sobre_ventas <= 0)) || 
    valuationMethod === 'PROMEDIO'
  ) {
    return [calculateNewMovement(movements, newMovement, valuationMethod)];
  }

  // Si es salida PEPS o UEPS
  if (newMovement.salida && newMovement.salida > 0) {
    let desglose: { cantidad: number; costoUnitario: number }[] = [];
    if (valuationMethod === 'PEPS') {
      desglose = desglosePEPS(movements, newMovement.salida);
    } else if (valuationMethod === 'UEPS') {
      desglose = desgloseUEPS(movements, newMovement.salida);
    }

    const ultimoMovimiento = movements[movements.length - 1];
    let inv_inicial = ultimoMovimiento ? ultimoMovimiento.inv_final : 0;
    let saldo = ultimoMovimiento ? ultimoMovimiento.saldo : 0;

    // Crear un movimiento por cada sublote
    return desglose.map((sub, idx) => {
      const inv_final = inv_inicial - sub.cantidad;
      const debe = 0;
      const haber = sub.cantidad * sub.costoUnitario;
      saldo = (saldo || 0) - haber;
      const movimiento: InventoryMovement = {
        ...newMovement,
        inv_inicial,
        inv_final,
        entrada: 0,
        salida: sub.cantidad,
        costo_unitario: sub.costoUnitario,
        costo_medio: 0,
        debe,
        haber,
        saldo,
        devolucion_sobre_ventas: 0,
        devolucion_sobre_compras: 0,
        Detalle: newMovement.Detalle + (desglose.length > 1 ? ` (lote ${idx + 1})` : '')
      };
      inv_inicial = inv_final;
      return movimiento;
    });
  }
  
  // Si es devolución sobre ventas PEPS o UEPS
  if (newMovement.devolucion_sobre_ventas && newMovement.devolucion_sobre_ventas > 0) {
    let desglose = desgloseDevolucionVentas(movements, newMovement.devolucion_sobre_ventas, valuationMethod);
    
    const ultimoMovimiento = movements[movements.length - 1];
    let inv_inicial = ultimoMovimiento ? ultimoMovimiento.inv_final : 0;
    let saldo = ultimoMovimiento ? ultimoMovimiento.saldo : 0;

    // Crear un movimiento por cada sublote
    return desglose.map((sub, idx) => {
      const inv_final = inv_inicial + sub.cantidad;
      const debe = sub.cantidad * sub.costoUnitario;
      const haber = 0;
      saldo = (saldo || 0) + debe;
      const movimiento: InventoryMovement = {
        ...newMovement,
        inv_inicial,
        inv_final,
        entrada: 0,
        salida: 0,
        costo_unitario: sub.costoUnitario,
        costo_medio: 0,
        debe,
        haber,
        saldo,
        devolucion_sobre_ventas: sub.cantidad,
        devolucion_sobre_compras: 0,
        Detalle: newMovement.Detalle + (desglose.length > 1 ? ` (lote ${idx + 1})` : '')
      };
      inv_inicial = inv_final;
      return movimiento;
    });
  }
  
  return [calculateNewMovement(movements, newMovement, valuationMethod)];
};
