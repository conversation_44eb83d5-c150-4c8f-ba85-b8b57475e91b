import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { InventoryCard } from './components/InventoryCard';
import { AddMovementModal } from './components/AddMovementModal';
import { AddCardModal } from './components/AddCardModal';
import { useInventoryStore } from './store/inventoryStore';
import type { InventoryCard as InventoryCardType } from './types/inventory';

const App: React.FC = () => {
  const cards = useInventoryStore((state: any) => state.cards);
  const [selectedCard, setSelectedCard] = useState<string | null>(null);
  const [isMovementModalOpen, setIsMovementModalOpen] = useState(false);
  const [isCardModalOpen, setIsCardModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const handleAddMovement = (numero_tarjeta: string) => {
    setSelectedCard(numero_tarjeta);
    setIsMovementModalOpen(true);
  };

  const handleClearData = () => {
    localStorage.clear();
    window.location.reload();
  };

  return (
    <div className="min-vh-100 bg-light">
      <div className="container-fluid py-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-4"
        >
          <div className="d-flex justify-content-between align-items-center">
            <h1 className="mb-0">Sistema de Control de Inventario</h1>
            {cards.length > 0 && (
              <div className="d-flex gap-2">
                <button
                  className="btn btn-danger"
                  onClick={() => setIsConfirmModalOpen(true)}
                >
                  <i className="bi bi-trash me-2"></i>
                  Eliminar Tarjeta
                </button>
                <button
                  className="btn btn-primary"
                  onClick={() => setIsCardModalOpen(true)}
                >
                  <i className="bi bi-plus-circle me-2"></i>
                  Nueva Tarjeta
                </button>
              </div>
            )}
          </div>
        </motion.div>

        <div className="row g-4">
          {cards.length === 0 ? (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="col-12 text-center py-5"
            >
              <div className="card shadow-lg border-0 bg-white">
                <div className="card-body p-5">
                  <div className="display-1 text-primary mb-4">
                    <i className="bi bi-box-seam"></i>
                  </div>
                  <h2 className="mb-4">¡Bienvenido al Sistema de Control de Inventario!</h2>
                  <p className="lead text-muted mb-5">
                    Comienza creando tu primera tarjeta de almacén para gestionar tus productos.
                  </p>
                  <button
                    className="btn btn-primary btn-lg px-5 py-3"
                    onClick={() => setIsCardModalOpen(true)}
                  >
                    <i className="bi bi-plus-circle me-2"></i>
                    Crear Primera Tarjeta
                  </button>
                </div>
              </div>
            </motion.div>
          ) : (
            cards.map((card: InventoryCardType) => (
              <div key={card.product.numero_tarjeta} className="col-12">
                <InventoryCard card={card} />
                <div className="text-end mt-2">
                  <button
                    className="btn btn-primary"
                    onClick={() => handleAddMovement(card.product.numero_tarjeta)}
                  >
                    <i className="bi bi-plus-circle me-2"></i>
                    Agregar Movimiento
                  </button>
                </div>
              </div>
            ))
          )}
        </div>

        {selectedCard && (
          <AddMovementModal
            isOpen={isMovementModalOpen}
            onClose={() => {
              setIsMovementModalOpen(false);
              setSelectedCard(null);
            }}
            numero_tarjeta={selectedCard}
          />
        )}

        <AddCardModal
          isOpen={isCardModalOpen}
          onClose={() => setIsCardModalOpen(false)}
        />

        {isConfirmModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="modal fade show d-block"
            tabIndex={-1}
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
          >
            <div className="modal-dialog modal-dialog-centered">
              <div className="modal-content border-0 shadow-lg">
                <div className="modal-header bg-gradient bg-danger text-white">
                  <h5 className="modal-title">
                    <i className="bi bi-exclamation-triangle-fill me-2"></i>
                    Confirmar Eliminación
                  </h5>
                  <button 
                    type="button" 
                    className="btn-close btn-close-white" 
                    onClick={() => setIsConfirmModalOpen(false)}
                  ></button>
                </div>
                <div className="modal-body text-center py-5">
                  <div className="display-1 text-danger mb-4" style={{ color: '#dc3545' }}>
                    <i className="bi bi-trash"></i>
                  </div>
                  <h4 className="mb-3 fw-bold" style={{ color: '#212529' }}>¿Estás seguro de que deseas eliminar todos los datos?</h4>
                  <p className="text-muted fs-5">
                    Esta acción eliminará todas las tarjetas y movimientos registrados.
                    <br />
                    Esta acción no se puede deshacer.
                  </p>
                </div>
                <div className="modal-footer border-0 bg-light">
                  <button 
                    type="button" 
                    className="btn btn-light btn-lg px-4 border" 
                    onClick={() => setIsConfirmModalOpen(false)}
                  >
                    <i className="bi bi-x-circle me-2"></i>
                    Cancelar
                  </button>
                  <button 
                    type="button" 
                    className="btn btn-danger btn-lg px-4" 
                    onClick={handleClearData}
                    style={{ backgroundColor: '#dc3545' }}
                  >
                    <i className="bi bi-trash me-2"></i>
                    Eliminar Todo
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default App;
