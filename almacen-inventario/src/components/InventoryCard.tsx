import React, { useState } from 'react';
import { motion } from 'framer-motion';
import type { InventoryCard as InventoryCardType, ValuationMethod, InventoryMovement } from '../types/inventory';
import { useInventoryStore } from '../store/inventoryStore';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { <PERSON><PERSON>, <PERSON>dalHeader, ModalBody, ModalFooter, ModalTitle } from 'react-bootstrap';

interface InventoryCardProps {
  card: InventoryCardType;
}

export const InventoryCard: React.FC<InventoryCardProps> = ({ card }) => {
  const { product, movements, valuationMethod } = card;
  const setValuationMethod = useInventoryStore((state: any) => state.setValuationMethod);
  const clearCardData = useInventoryStore((state: any) => state.clearCardData);
  const [showClearModal, setShowClearModal] = useState(false);

  const handleValuationMethodChange = (method: ValuationMethod) => {
    setValuationMethod(product.numero_tarjeta, method);
  };

  const handleClearCard = () => {
    clearCardData(product.numero_tarjeta);
    setShowClearModal(false);
  };

  const getValuationMethodDescription = (method: ValuationMethod) => {
    switch (method) {
      case 'PEPS':
        return 'Primeras Entradas, Primeras Salidas - Se consumen primero las existencias más antiguas';
      case 'UEPS':
        return 'Últimas Entradas, Primeras Salidas - Se consumen primero las existencias más recientes';
      case 'PROMEDIO':
        return 'Promedio Ponderado - Se calcula un costo promedio basado en todas las existencias';
      default:
        return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card shadow-lg mb-4 border-0"
    >
      <div className="card-header bg-gradient bg-primary text-white d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center py-3">
        <h3 className="mb-2 mb-md-0 d-flex align-items-center">
          <i className="bi bi-box-seam me-2"></i>
          Tarjeta de Almacén - {product.articulo}
        </h3>
        <div className="d-flex flex-column flex-md-row align-items-stretch align-items-md-center gap-2">
          <button
            className="btn btn-sm btn-light"
            onClick={() => setShowClearModal(true)}
            title="Limpiar valores de la tarjeta"
          >
            <i className="bi bi-trash me-1"></i>
            Limpiar Valores
          </button>
          <select
            className="form-select form-select-sm w-100 w-md-auto border-0 bg-white bg-opacity-10 text-white"
            value={valuationMethod}
            onChange={(e) => handleValuationMethodChange(e.target.value as ValuationMethod)}
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.1) !important',
              color: 'white !important',
              cursor: 'pointer',
              backdropFilter: 'blur(4px)'
            }}
          >
            <option value="PEPS" className="text-dark">PEPS - Primeras Entradas, Primeras Salidas</option>
            <option value="UEPS" className="text-dark">UEPS - Últimas Entradas, Primeras Salidas</option>
            <option value="PROMEDIO" className="text-dark">Promedio Ponderado</option>
          </select>
        </div>
      </div>
      <div className="card-body p-2 p-md-4">
        <div className="row g-3 mb-4">
          <div className="col-12 col-md-6">
            <div className="card bg-light border-0 shadow-sm h-100">
              <div className="card-body">
                <h5 className="card-title mb-3 d-flex align-items-center">
                  <i className="bi bi-info-circle me-2 text-primary"></i>
                  Información del Producto
                </h5>
                <div className="d-flex align-items-center mb-2">
                  <i className="bi bi-card-text text-primary me-2"></i>
                  <p className="mb-0 small"><strong>Descripción:</strong> {product.descripcion}</p>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <i className="bi bi-building text-primary me-2"></i>
                  <p className="mb-0 small"><strong>Proveedor:</strong> {product.proveedor}</p>
                </div>
                <div className="d-flex align-items-center mb-2">
                  <i className="bi bi-geo-alt text-primary me-2"></i>
                  <p className="mb-0 small"><strong>Ubicación:</strong> {product.ubicacion}</p>
                </div>
                <div className="d-flex align-items-center">
                  <i className="bi bi-calendar text-primary me-2"></i>
                  <p className="mb-0 small"><strong>Período:</strong> {format(new Date(product.periodo + '-01'), 'MMMM yyyy', { locale: es })}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="col-12 col-md-6">
            <div className="alert alert-info border-0 shadow-sm d-flex align-items-center h-100">
              <i className="bi bi-info-circle-fill me-3 fs-4"></i>
              <div>
                <h6 className="mb-2">Método de Valuación</h6>
                <small>{getValuationMethodDescription(valuationMethod)}</small>
              </div>
            </div>
          </div>
        </div>

        <div className="table-responsive">
          <table className="table table-hover align-middle">
            <thead>
              <tr className="bg-light">
                <th className="border-0">Fecha</th>
                <th className="border-0">Detalle</th>
                <th className="border-0 text-center">Inv. Inicial</th>
                <th className="border-0 text-center">Entrada</th>
                <th className="border-0 text-center">Salida</th>
                <th className="border-0 text-center">Dev/Venta</th>
                <th className="border-0 text-center">Dev/Compra</th>
                <th className="border-0 text-center">Inv. Final</th>
                <th className="border-0 text-center">Costo Unit.</th>
                <th className="border-0 text-center">Costo Medio</th>
                <th className="border-0 text-center">Debe</th>
                <th className="border-0 text-center">Haber</th>
                <th className="border-0 text-center">Saldo</th>
              </tr>
            </thead>
            <tbody>
              {/* Agrupar movimientos por fecha y detalle base */}
              {(() => {
                // Extraer el detalle base (sin sufijo) para agrupar
                const getDetalleBase = (detalle: string) => detalle.replace(/ \([0-9]+\)$/, '').trim();
                // Agrupar movimientos por fecha y detalle base
                const grupos: { [key: string]: InventoryMovement[] } = {};
                movements.forEach((mov) => {
                  const key = mov.fecha + '|' + getDetalleBase(mov.Detalle);
                  if (!grupos[key]) grupos[key] = [];
                  grupos[key].push(mov);
                });
                // Renderizar cada grupo
                return Object.entries(grupos).map(([key, movs], idx) => {
                  const [fecha, detalleBase] = key.split('|');
                  return (
                    <React.Fragment key={key}>
                      {/* Fila principal */}
                      <motion.tr
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: idx * 0.1 }}
                        className="border-bottom fw-bold bg-light"
                      >
                        <td className="fw-medium">{format(new Date(fecha + 'T12:00:00'), 'dd/MM/yyyy')}</td>
                        <td className="fw-medium">{detalleBase}</td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                        <td className="text-center"></td>
                      </motion.tr>
                      {/* Subfilas para cada movimiento */}
                      {movs.map((movement, subIdx) => {
                        let tipo = '';
                        let color = '';
                        let icono = '';
                        if (movement.entrada && movement.entrada > 0) {
                          tipo = 'Entrada'; color = 'text-success'; icono = 'bi bi-arrow-down-circle';
                        } else if (movement.salida && movement.salida > 0) {
                          tipo = 'Salida'; color = 'text-danger'; icono = 'bi bi-arrow-up-circle';
                        } else if (movement.devolucion_sobre_ventas && movement.devolucion_sobre_ventas > 0) {
                          tipo = 'Dev. sobre Ventas'; color = 'text-warning'; icono = 'bi bi-arrow-return-left';
                        } else if (movement.devolucion_sobre_compras && movement.devolucion_sobre_compras > 0) {
                          tipo = 'Dev. sobre Compras'; color = 'text-warning'; icono = 'bi bi-arrow-return-right';
                        }
                        return (
                          <motion.tr
                            key={subIdx}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: idx * 0.1 + subIdx * 0.05 }}
                            className="border-bottom bg-white"
                          >
                            <td></td>
                            <td className={`fw-medium ${color}`}>
                              <i className={`${icono} me-1`}></i>
                              {tipo}
                            </td>
                            <td className="text-center">{movement.inv_inicial !== undefined && movement.inv_inicial !== 0 ? movement.inv_inicial : ''}</td>
                            <td className="text-center text-success fw-medium">{movement.entrada && movement.entrada > 0 ? movement.entrada : ''}</td>
                            <td className="text-center text-danger fw-medium">{movement.salida && movement.salida > 0 ? movement.salida : ''}</td>
                            <td className="text-center text-warning fw-medium">{movement.devolucion_sobre_ventas && movement.devolucion_sobre_ventas > 0 ? movement.devolucion_sobre_ventas : ''}</td>
                            <td className="text-center text-warning fw-medium">{movement.devolucion_sobre_compras && movement.devolucion_sobre_compras > 0 ? movement.devolucion_sobre_compras : ''}</td>
                            <td className="text-center fw-medium">{movement.inv_final !== undefined && movement.inv_final !== 0 ? movement.inv_final : ''}</td>
                            <td className="text-center">{movement.costo_unitario !== undefined && movement.costo_unitario !== 0 ? `$${movement.costo_unitario.toFixed(2)}` : ''}</td>
                            <td className="text-center">{movement.costo_medio !== undefined && movement.costo_medio !== 0 ? `$${movement.costo_medio.toFixed(2)}` : ''}</td>
                            <td className="text-center text-success fw-medium">{movement.debe !== undefined && movement.debe !== 0 ? `$${movement.debe.toFixed(2)}` : ''}</td>
                            <td className="text-center text-danger fw-medium">{movement.haber !== undefined && movement.haber !== 0 ? `$${movement.haber.toFixed(2)}` : ''}</td>
                            <td className={`text-center fw-bold ${movement.saldo !== undefined && movement.saldo >= 0 ? 'text-success' : 'text-danger'}`}>{movement.saldo !== undefined && movement.saldo !== 0 ? `$${Math.abs(movement.saldo).toFixed(2)}` : ''}</td>
                          </motion.tr>
                        );
                      })}
                    </React.Fragment>
                  );
                });
              })()}
            </tbody>
          </table>
        </div>
      </div>

      <Modal show={showClearModal} onHide={() => setShowClearModal(false)} centered>
        <ModalHeader closeButton className="bg-danger text-white">
          <ModalTitle>
            <i className="bi bi-exclamation-triangle-fill me-2"></i>
            Confirmar Limpieza
          </ModalTitle>
        </ModalHeader>
        <ModalBody className="text-center py-4">
          <i className="bi bi-trash text-danger" style={{ fontSize: '3rem' }}></i>
          <p className="mt-3 mb-0 fs-5">¿Estás seguro de que deseas limpiar todos los valores de esta tarjeta?</p>
          <p className="text-muted">Esta acción no se puede deshacer.</p>
        </ModalBody>
        <ModalFooter className="bg-light justify-content-center">
          <button type="button" className="btn btn-secondary px-4" onClick={() => setShowClearModal(false)}>
            <i className="bi bi-x-circle me-2"></i>
            Cancelar
          </button>
          <button type="button" className="btn btn-danger px-4" onClick={handleClearCard}>
            <i className="bi bi-trash me-2"></i>
            Limpiar Valores
          </button>
        </ModalFooter>
      </Modal>
    </motion.div>
  );
}; 