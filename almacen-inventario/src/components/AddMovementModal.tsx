import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import type { InventoryMovement, InventoryCard } from '../types/inventory';
import { useInventoryStore } from '../store/inventoryStore';
import { format } from 'date-fns';
import { calculateNewMovement, calculateNewMovementsWithDesglose } from '../utils/inventoryCalculations';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { es as localeEs } from 'date-fns/locale';

interface AddMovementModalProps {
  isOpen: boolean;
  onClose: () => void;
  numero_tarjeta: string;
}

export const AddMovementModal: React.FC<AddMovementModalProps> = ({
  isOpen,
  onClose,
  numero_tarjeta,
}) => {
  const addMovement = useInventoryStore((state: any) => state.addMovement);
  const cards = useInventoryStore((state: any) => state.cards);
  const currentCard = cards.find((card: InventoryCard) => card.product.numero_tarjeta === numero_tarjeta);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const today = new Date();
  const [movement, setMovement] = useState<Partial<InventoryMovement>>({
    fecha: format(today, 'yyyy-MM-dd'),
    Detalle: '',
    entrada: 0,
    salida: 0,
    costo_unitario: 0,
    devolucion_sobre_ventas: 0,
    devolucion_sobre_compras: 0,
  });

  // Obtener el período de la tarjeta actual (yyyy-MM)
  const periodo = currentCard?.product.periodo || format(today, 'yyyy-MM');
  // Calcular el primer y último día del mes
  const minDate = new Date(periodo + '-01T12:00:00');
  const maxDate = new Date(minDate.getFullYear(), minDate.getMonth() + 1, 0, 12, 0, 0, 0);

  // Inicializar la fecha del movimiento con el primer día del mes del período
  useEffect(() => {
    if (isOpen && currentCard) {
      setMovement((prev) => ({
        ...prev,
        fecha: format(minDate, 'yyyy-MM-dd'),
      }));
    }
    // eslint-disable-next-line
  }, [isOpen, currentCard]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentCard) return;

    // Verificar si el detalle ya existe
    const detalleExists = currentCard.movements.some(
      (m: InventoryMovement) => m.Detalle.toLowerCase() === movement.Detalle?.toLowerCase()
    );

    if (detalleExists) {
      setErrorMessage('Ya existe un movimiento con este detalle');
      setShowErrorModal(true);
      return;
    }

    try {
      // Desglosar el movimiento en varios si hay más de un valor
      const movimientosInternos: Partial<InventoryMovement>[] = [];
      const base = {
        fecha: movement.fecha || format(new Date(), 'yyyy-MM-dd'),
        Detalle: movement.Detalle || '',
        costo_unitario: movement.costo_unitario || 0,
      };
      if (movement.entrada && movement.entrada > 0) {
        movimientosInternos.push({
          ...base,
          entrada: movement.entrada,
          salida: undefined,
          devolucion_sobre_ventas: undefined,
          devolucion_sobre_compras: undefined,
        });
      }
      if (movement.salida && movement.salida > 0) {
        movimientosInternos.push({
          ...base,
          entrada: undefined,
          salida: movement.salida,
          devolucion_sobre_ventas: undefined,
          devolucion_sobre_compras: undefined,
        });
      }
      if (movement.devolucion_sobre_ventas && movement.devolucion_sobre_ventas > 0) {
        movimientosInternos.push({
          ...base,
          entrada: undefined,
          salida: undefined,
          devolucion_sobre_ventas: movement.devolucion_sobre_ventas,
          devolucion_sobre_compras: undefined,
        });
      }
      if (movement.devolucion_sobre_compras && movement.devolucion_sobre_compras > 0) {
        movimientosInternos.push({
          ...base,
          entrada: undefined,
          salida: undefined,
          devolucion_sobre_ventas: undefined,
          devolucion_sobre_compras: movement.devolucion_sobre_compras,
        });
      }

      // Si no hay más de un valor, agregar el movimiento normal
      if (movimientosInternos.length === 0) {
        // Si es salida o devolución sobre ventas PEPS o UEPS, usar el desglose
        if (((movement.salida && movement.salida > 0) || (movement.devolucion_sobre_ventas && movement.devolucion_sobre_ventas > 0)) && (currentCard.valuationMethod === 'PEPS' || currentCard.valuationMethod === 'UEPS')) {
          const movimientosDesglosados = calculateNewMovementsWithDesglose(
            currentCard.movements,
            {
              ...movement,
              fecha: movement.fecha || format(new Date(), 'yyyy-MM-dd'),
              Detalle: movement.Detalle || '',
              entrada: movement.entrada || 0,
              salida: movement.salida || 0,
              costo_unitario: movement.costo_unitario || 0,
              devolucion_sobre_ventas: movement.devolucion_sobre_ventas || 0,
              devolucion_sobre_compras: movement.devolucion_sobre_compras || 0,
            } as InventoryMovement,
            currentCard.valuationMethod
          );
          movimientosDesglosados.forEach(mov => addMovement(numero_tarjeta, mov));
        } else {
          const newMovement = calculateNewMovement(
            currentCard.movements,
            {
              ...movement,
              fecha: movement.fecha || format(new Date(), 'yyyy-MM-dd'),
              Detalle: movement.Detalle || '',
              entrada: movement.entrada || 0,
              salida: movement.salida || 0,
              costo_unitario: movement.costo_unitario || 0,
              devolucion_sobre_ventas: movement.devolucion_sobre_ventas || 0,
              devolucion_sobre_compras: movement.devolucion_sobre_compras || 0,
            } as InventoryMovement,
            currentCard.valuationMethod
          );
          addMovement(numero_tarjeta, newMovement);
        }
      } else {
        // Agregar cada submovimiento como un movimiento independiente
        let movimientosPrevios = [...currentCard.movements];
        movimientosInternos.forEach((mov) => {
          // Si es salida o devolución sobre ventas PEPS o UEPS, usar el desglose
          if (((mov.salida && mov.salida > 0) || (mov.devolucion_sobre_ventas && mov.devolucion_sobre_ventas > 0)) && (currentCard.valuationMethod === 'PEPS' || currentCard.valuationMethod === 'UEPS')) {
            const movimientosDesglosados = calculateNewMovementsWithDesglose(
              movimientosPrevios,
              {
                ...mov,
                fecha: mov.fecha!,
                Detalle: mov.Detalle!.replace(/ \([0-9]+\)$/, ''),
                entrada: mov.entrada || 0,
                salida: mov.salida || 0,
                costo_unitario: mov.costo_unitario || 0,
                devolucion_sobre_ventas: mov.devolucion_sobre_ventas || 0,
                devolucion_sobre_compras: mov.devolucion_sobre_compras || 0,
              } as InventoryMovement,
              currentCard.valuationMethod
            );
            movimientosDesglosados.forEach(movDesglose => {
              addMovement(numero_tarjeta, movDesglose);
            });
            movimientosPrevios = [...movimientosPrevios, ...movimientosDesglosados];
          } else {
            const newMovement = calculateNewMovement(
              movimientosPrevios,
              {
                ...mov,
                fecha: mov.fecha!,
                Detalle: mov.Detalle!.replace(/ \([0-9]+\)$/, ''),
                entrada: mov.entrada || 0,
                salida: mov.salida || 0,
                costo_unitario: mov.costo_unitario || 0,
                devolucion_sobre_ventas: mov.devolucion_sobre_ventas || 0,
                devolucion_sobre_compras: mov.devolucion_sobre_compras || 0,
              } as InventoryMovement,
              currentCard.valuationMethod
            );
            addMovement(numero_tarjeta, newMovement);
            movimientosPrevios = [...movimientosPrevios, newMovement];
          }
        });
      }
      onClose();
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage('No hay suficiente inventario disponible');
      }
      setShowErrorModal(true);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="modal fade show d-block"
        tabIndex={-1}
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
      >
        <div className="modal-dialog modal-dialog-centered" style={{ maxWidth: '700px', width: '100%' }}>
          <div className="modal-content">
            <div className="modal-header bg-primary text-white">
              <h5 className="modal-title">
                <i className="bi bi-plus-circle-fill me-2"></i>
                Nuevo Movimiento
              </h5>
              <button type="button" className="btn-close btn-close-white" onClick={onClose}></button>
            </div>
            <div className="modal-body">
              <form onSubmit={handleSubmit}>
                <div className="row g-3 mb-4">
                  <div className="col-12 col-md-3">
                    <label className="form-label fw-bold">Fecha</label>
                    <DatePicker
                      selected={movement.fecha ? new Date(movement.fecha + 'T12:00:00') : null}
                      onChange={(date: Date | null) => {
                        if (date) {
                          const fixedDate = new Date(date);
                          fixedDate.setHours(12, 0, 0, 0);
                          setMovement({ ...movement, fecha: format(fixedDate, 'yyyy-MM-dd') });
                        }
                      }}
                      minDate={minDate}
                      maxDate={maxDate}
                      dateFormat="yyyy-MM-dd"
                      className="form-control form-control-lg"
                      locale={localeEs}
                      placeholderText="Selecciona la fecha"
                      required
                      showPopperArrow={false}
                      popperPlacement="bottom"
                    />
                  </div>
                  <div className="col-12 col-md-9">
                    <label className="form-label fw-bold">Detalle</label>
                    <input
                      type="text"
                      className="form-control form-control-lg"
                      value={movement.Detalle}
                      onChange={(e) => setMovement({ ...movement, Detalle: e.target.value })}
                      placeholder="Ej: Factura #12345"
                      required
                    />
                  </div>
                </div>
                <div className="row g-3 mb-4">
                  <div className="col-12 col-md-3">
                    <label className="form-label fw-bold">Entrada</label>
                    <div className="input-group input-group-lg">
                      <input
                        type="number"
                        className="form-control"
                        value={movement.entrada}
                        onChange={(e) => setMovement({ ...movement, entrada: Number(e.target.value) })}
                        placeholder="Ej: 10"
                        min="0"
                        step="1"
                      />
                      <span className="input-group-text bg-success text-white">
                        <i className="bi bi-arrow-down-circle-fill"></i>
                      </span>
                    </div>
                  </div>
                  <div className="col-12 col-md-3">
                    <label className="form-label fw-bold">Salida</label>
                    <div className="input-group input-group-lg">
                      <input
                        type="number"
                        className="form-control"
                        value={movement.salida}
                        onChange={(e) => setMovement({ ...movement, salida: Number(e.target.value) })}
                        placeholder="Ej: 5"
                        min="0"
                        step="1"
                      />
                      <span className="input-group-text bg-danger text-white">
                        <i className="bi bi-arrow-up-circle-fill"></i>
                      </span>
                    </div>
                  </div>
                  <div className="col-12 col-md-3">
                    <label className="form-label fw-bold">Dev/Venta</label>
                    <div className="input-group input-group-lg">
                      <input
                        type="number"
                        className="form-control"
                        value={movement.devolucion_sobre_ventas}
                        onChange={(e) => setMovement({ ...movement, devolucion_sobre_ventas: Number(e.target.value) })}
                        placeholder="Ej: 2"
                        min="0"
                        step="1"
                      />
                      <span className="input-group-text bg-warning text-dark">
                        <i className="bi bi-arrow-return-left"></i>
                      </span>
                    </div>
                  </div>
                  <div className="col-12 col-md-3">
                    <label className="form-label fw-bold">Dev/Compra</label>
                    <div className="input-group input-group-lg">
                      <input
                        type="number"
                        className="form-control"
                        value={movement.devolucion_sobre_compras}
                        onChange={(e) => setMovement({ ...movement, devolucion_sobre_compras: Number(e.target.value) })}
                        placeholder="Ej: 1"
                        min="0"
                        step="1"
                      />
                      <span className="input-group-text bg-warning text-dark">
                        <i className="bi bi-arrow-return-right"></i>
                      </span>
                    </div>
                  </div>
                </div>
                <div className="row g-3 mb-4">
                  <div className="col-12 col-md-4 offset-md-8">
                    <label className="form-label fw-bold">Costo Unitario</label>
                    <div className="input-group input-group-lg">
                      <span className="input-group-text bg-primary text-white">
                        <i className="bi bi-currency-dollar"></i>
                      </span>
                      <input
                        type="number"
                        className="form-control"
                        value={movement.costo_unitario}
                        onChange={(e) => setMovement({ ...movement, costo_unitario: Number(e.target.value) })}
                        placeholder="Ej: 1500.00"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                  </div>
                </div>
                <div className="alert alert-info d-flex align-items-center">
                  <i className="bi bi-info-circle-fill me-2 fs-4"></i>
                  <div>
                    <small>
                      Método de valuación actual: <strong>{currentCard?.valuationMethod}</strong>
                      {currentCard?.valuationMethod === 'PEPS' && ' - Primero en entrar, primero en salir'}
                      {currentCard?.valuationMethod === 'UEPS' && ' - Último en entrar, primero en salir'}
                      {currentCard?.valuationMethod === 'PROMEDIO' && ' - Promedio ponderado'}
                    </small>
                  </div>
                </div>
                <div className="modal-footer border-0 d-flex flex-column flex-md-row justify-content-center gap-2">
                  <button type="button" className="btn btn-secondary btn-lg px-4 w-100 w-md-auto" onClick={onClose}>
                    <i className="bi bi-x-circle me-2"></i>
                    Cancelar
                  </button>
                  <button type="submit" className="btn btn-primary btn-lg px-4 w-100 w-md-auto">
                    <i className="bi bi-plus-circle me-2"></i>
                    Agregar Movimiento
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Modal de Error */}
      {showErrorModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="modal fade show d-block"
          tabIndex={-1}
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        >
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header bg-danger text-white">
                <h5 className="modal-title">
                  <i className="bi bi-exclamation-triangle-fill me-2"></i>
                  Error
                </h5>
                <button type="button" className="btn-close btn-close-white" onClick={() => setShowErrorModal(false)}></button>
              </div>
              <div className="modal-body text-center py-4">
                <i className="bi bi-x-circle text-danger" style={{ fontSize: '3rem' }}></i>
                <p className="mt-3 mb-0 fs-5">{errorMessage}</p>
              </div>
              <div className="modal-footer justify-content-center border-0">
                <button 
                  type="button" 
                  className="btn btn-danger px-4" 
                  onClick={() => setShowErrorModal(false)}
                >
                  Entendido
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </>
  );
}; 