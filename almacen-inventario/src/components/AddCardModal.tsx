import React, { useState } from 'react';
import { motion } from 'framer-motion';
import type { Product, InventoryCard } from '../types/inventory';
import { useInventoryStore } from '../store/inventoryStore';
import { format } from 'date-fns';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { es as localeEs } from 'date-fns/locale';

interface AddCardModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddCardModal: React.FC<AddCardModalProps> = ({ isOpen, onClose }) => {
  const addCard = useInventoryStore((state: any) => state.addCard);
  const cards = useInventoryStore((state: any) => state.cards);
  const deleteCard = useInventoryStore((state: any) => state.deleteCard);
  const [product, setProduct] = useState<Partial<Product>>({
    articulo: '',
    descripcion: '',
    proveedor: '',
    ubicacion: '',
    periodo: format(new Date(), 'yyyy-MM'),
    numero_tarjeta: `TARJ-${Date.now()}`,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!product.articulo || !product.descripcion) return;

    // Si existe una tarjeta anterior, la eliminamos
    if (cards.length > 0) {
      const tarjetaAnterior = cards[0];
      deleteCard(tarjetaAnterior.product.numero_tarjeta);
    }

    const newCard: InventoryCard = {
      product: product as Product,
      movements: [],
      valuationMethod: 'PROMEDIO',
    };

    addCard(newCard);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="modal fade show d-block"
      tabIndex={-1}
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
    >
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content border-0 shadow-lg" style={{ borderRadius: '1.5rem', background: 'var(--background-color, #fff)' }}>
          <div className="modal-header bg-gradient bg-primary text-white d-flex flex-column align-items-center" style={{ borderRadius: '1.5rem 1.5rem 0 0', background: 'linear-gradient(135deg, #4f46e5, #4338ca)' }}>
            <div className="display-3 mb-2">
              <i className="bi bi-card-list"></i>
            </div>
            <h3 className="modal-title fw-bold mb-0">
              Nueva Tarjeta de Almacén
            </h3>
            <button type="button" className="btn-close btn-close-white position-absolute end-0 top-0 m-3" onClick={onClose}></button>
          </div>
          <div className="modal-body p-5">
            <form onSubmit={handleSubmit}>
              <div className="row mb-4 g-4">
                <div className="col-md-6">
                  <label className="form-label fw-bold">Artículo</label>
                  <div className="input-group input-group-lg shadow-sm rounded-3">
                    <span className="input-group-text bg-primary text-white fs-4">
                      <i className="bi bi-box-seam"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control border-0 bg-light"
                      value={product.articulo}
                      onChange={(e) => setProduct({ ...product, articulo: e.target.value })}
                      placeholder="Ej: Grafica de video"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label fw-bold">Descripción</label>
                  <div className="input-group input-group-lg shadow-sm rounded-3">
                    <span className="input-group-text bg-info text-white fs-4">
                      <i className="bi bi-card-text"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control border-0 bg-light"
                      value={product.descripcion}
                      onChange={(e) => setProduct({ ...product, descripcion: e.target.value })}
                      placeholder="Ej: RTX 3060"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="row mb-4 g-4">
                <div className="col-md-6">
                  <label className="form-label fw-bold">Proveedor</label>
                  <div className="input-group input-group-lg shadow-sm rounded-3">
                    <span className="input-group-text bg-success text-white fs-4">
                      <i className="bi bi-building"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control border-0 bg-light"
                      value={product.proveedor}
                      onChange={(e) => setProduct({ ...product, proveedor: e.target.value })}
                      placeholder="Ej: Nvidia S.A. de C.V."
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <label className="form-label fw-bold">Ubicación</label>
                  <div className="input-group input-group-lg shadow-sm rounded-3">
                    <span className="input-group-text bg-warning text-white fs-4">
                      <i className="bi bi-geo-alt"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control border-0 bg-light"
                      value={product.ubicacion}
                      onChange={(e) => setProduct({ ...product, ubicacion: e.target.value })}
                      placeholder="Ej: Almacén Principal"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="row mb-4 g-4">
                <div className="col-12">
                  <label className="form-label fw-bold">Período</label>
                  <div className="input-group input-group-lg shadow-sm rounded-3">
                    <span className="input-group-text bg-primary text-white fs-4">
                      <i className="bi bi-calendar-month"></i>
                    </span>
                    <DatePicker
                      selected={product.periodo ? new Date(product.periodo + '-01T12:00:00') : null}
                      onChange={(date: Date | null) => {
                        if (date) {
                          setProduct({ ...product, periodo: format(date, 'yyyy-MM') });
                        }
                      }}
                      dateFormat="yyyy-MM"
                      showMonthYearPicker
                      showFullMonthYearPicker
                      className="form-control border-0 bg-light"
                      locale={localeEs}
                      placeholderText="Selecciona mes y año"
                      required
                    />
                  </div>
                </div>
              </div>
              {cards.length > 0 && (
                <div className="alert d-flex align-items-center shadow-sm rounded-3 mt-4" style={{ background: '#fffbe6', border: '2px solid #ffe066' }}>
                  <i className="bi bi-exclamation-triangle-fill me-3 fs-2 text-warning"></i>
                  <div className="fs-5 fw-bold text-dark">
                    Se eliminará la tarjeta actual y se creará una nueva.
                  </div>
                </div>
              )}
              <div className="modal-footer border-0 bg-light rounded-bottom justify-content-center mt-4">
                <button type="button" className="btn btn-secondary btn-lg px-4 me-2" onClick={onClose} style={{ borderRadius: '0.75rem' }}>
                  <i className="bi bi-x-circle me-2"></i>
                  Cancelar
                </button>
                <button type="submit" className="btn btn-primary btn-lg px-4" style={{ borderRadius: '0.75rem' }}>
                  <i className="bi bi-plus-circle me-2"></i>
                  Crear Tarjeta
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </motion.div>
  );
}; 