export type ValuationMethod = 'PEPS' | 'UEPS' | 'PROMEDIO';

export interface Product {
  articulo: string;
  descripcion: string;
  proveedor: string;
  ubicacion: string;
  periodo: string;
  numero_tarjeta: string;
}

export interface InventoryMovement {
  fecha: string;
  Detalle: string;
  inv_inicial: number;
  entrada: number;
  salida: number;
  inv_final: number;
  costo_unitario: number;
  costo_medio: number;
  debe: number;
  haber: number;
  saldo: number;
  devolucion_sobre_ventas?: number;
  devolucion_sobre_compras?: number;
}

export interface InventoryCard {
  product: Product;
  movements: InventoryMovement[];
  valuationMethod: ValuationMethod;
}

export interface InventoryState {
  cards: InventoryCard[];
  addCard: (card: InventoryCard) => void;
  updateCard: (numero_tarjeta: string, card: InventoryCard) => void;
  deleteCard: (numero_tarjeta: string) => void;
  addMovement: (numero_tarjeta: string, movement: InventoryMovement) => void;
  setValuationMethod: (numero_tarjeta: string, method: ValuationMethod) => void;
} 